"""
Database Migration Utilities

This module provides utilities for managing database migrations using Alembic.
"""

import logging
import subprocess
import sys
from pathlib import Path
from typing import Optional, List

from alembic import command
from alembic.config import Config
from alembic.runtime.migration import MigrationContext
from alembic.script import ScriptDirectory

from ...config import get_settings
from .database import get_engine, init_db

logger = logging.getLogger(__name__)


def get_alembic_config() -> Config:
    """
    Get Alembic configuration.

    Returns:
        Alembic Config object
    """
    # Get the path to alembic.ini (it's in the backend root directory)
    backend_path = Path(__file__).parent.parent.parent.parent
    alembic_ini_path = backend_path / "alembic.ini"

    if not alembic_ini_path.exists():
        raise FileNotFoundError(f"Alembic configuration not found at {alembic_ini_path}")

    # Create Alembic config
    alembic_cfg = Config(str(alembic_ini_path))

    # Set the database URL from our settings
    settings = get_settings()
    alembic_cfg.set_main_option("sqlalchemy.url", settings.DATABASE_URL)

    return alembic_cfg


def get_current_revision() -> Optional[str]:
    """
    Get the current database revision.

    Returns:
        Current revision ID or None if no migrations have been applied
    """
    try:
        engine = get_engine()

        with engine.connect() as connection:
            context = MigrationContext.configure(connection)
            return context.get_current_revision()
    except Exception as e:
        logger.error(f"Failed to get current revision: {e}")
        return None


def get_available_revisions() -> List[str]:
    """
    Get list of available migration revisions.

    Returns:
        List of revision IDs
    """
    try:
        alembic_cfg = get_alembic_config()
        script_dir = ScriptDirectory.from_config(alembic_cfg)

        revisions = []
        for revision in script_dir.walk_revisions():
            revisions.append(revision.revision)

        return list(reversed(revisions))  # Return in chronological order
    except Exception as e:
        logger.error(f"Failed to get available revisions: {e}")
        return []


def run_migrations(target_revision: str = "head") -> bool:
    """
    Run database migrations.

    Args:
        target_revision: Target revision to migrate to (default: "head")

    Returns:
        True if migrations ran successfully, False otherwise
    """
    try:
        logger.info(f"Running migrations to revision: {target_revision}")

        # Ensure database is initialized
        init_db()

        # Get Alembic config
        alembic_cfg = get_alembic_config()

        # Run migrations
        command.upgrade(alembic_cfg, target_revision)

        logger.info("Migrations completed successfully")
        return True

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        return False


def create_migration(message: str, autogenerate: bool = True) -> bool:
    """
    Create a new migration.

    Args:
        message: Migration message/description
        autogenerate: Whether to auto-generate migration from model changes

    Returns:
        True if migration was created successfully, False otherwise
    """
    try:
        logger.info(f"Creating migration: {message}")

        # Get Alembic config
        alembic_cfg = get_alembic_config()

        # Create migration
        command.revision(
            alembic_cfg,
            message=message,
            autogenerate=autogenerate
        )

        logger.info("Migration created successfully")
        return True

    except Exception as e:
        logger.error(f"Failed to create migration: {e}")
        return False


def rollback_migration(target_revision: str = "-1") -> bool:
    """
    Rollback to a previous migration.

    Args:
        target_revision: Target revision to rollback to (default: "-1" for previous)

    Returns:
        True if rollback was successful, False otherwise
    """
    try:
        logger.warning(f"Rolling back to revision: {target_revision}")

        # Get Alembic config
        alembic_cfg = get_alembic_config()

        # Run downgrade
        command.downgrade(alembic_cfg, target_revision)

        logger.info("Rollback completed successfully")
        return True

    except Exception as e:
        logger.error(f"Rollback failed: {e}")
        return False


def get_migration_status() -> dict:
    """
    Get migration status information.

    Returns:
        Dictionary with migration status
    """
    try:
        current_revision = get_current_revision()
        available_revisions = get_available_revisions()

        status = {
            "current_revision": current_revision,
            "available_revisions": available_revisions,
            "is_up_to_date": current_revision == available_revisions[-1] if available_revisions else False,
            "pending_migrations": []
        }

        # Find pending migrations
        if current_revision and available_revisions:
            try:
                current_index = available_revisions.index(current_revision)
                status["pending_migrations"] = available_revisions[current_index + 1:]
            except ValueError:
                # Current revision not found in available revisions
                status["pending_migrations"] = available_revisions
        elif not current_revision and available_revisions:
            # No migrations applied yet
            status["pending_migrations"] = available_revisions

        return status

    except Exception as e:
        logger.error(f"Failed to get migration status: {e}")
        return {
            "current_revision": None,
            "available_revisions": [],
            "is_up_to_date": False,
            "pending_migrations": [],
            "error": str(e)
        }


def initialize_migrations() -> bool:
    """
    Initialize the migration system by running all pending migrations.

    Returns:
        True if initialization was successful, False otherwise
    """
    try:
        logger.info("Initializing migration system")

        # Check migration status
        status = get_migration_status()

        if status.get("error"):
            logger.error(f"Migration status check failed: {status['error']}")
            return False

        if status["is_up_to_date"]:
            logger.info("Database is already up to date")
            return True

        if status["pending_migrations"]:
            logger.info(f"Found {len(status['pending_migrations'])} pending migrations")
            return run_migrations()

        logger.info("No migrations to apply")
        return True

    except Exception as e:
        logger.error(f"Migration initialization failed: {e}")
        return False
