"""
Test CLI Commands

Provides testing operations including RAG pipeline testing.
"""

import argparse
from ..utils import run_script, print_header, print_info


def setup_test_parser(subparsers):
    """Set up test command parser."""
    test_parser = subparsers.add_parser('test', help='Testing operations')
    test_subparsers = test_parser.add_subparsers(dest='test_command', help='Test commands')
    
    # System integration tests
    system_parser = test_subparsers.add_parser('system', help='Run system integration tests')
    system_parser.add_argument('--mode', choices=['clean', 'full'], default='clean',
                              help='Test mode (clean=suppress logs, full=verbose)')
    
    # RAG pipeline tests
    rag_parser = test_subparsers.add_parser('rag', help='Test RAG pipeline functionality')
    rag_parser.add_argument('--mode', choices=['quick', 'full', 'embeddings', 'search', 'llm'], 
                           default='quick', help='RAG test mode')
    rag_parser.add_argument('--query', help='Test query for RAG pipeline')
    rag_parser.add_argument('--collection', help='Collection to test against')
    rag_parser.add_argument('--verbose', action='store_true', help='Verbose output')


def handle_test_command(args):
    """Handle test commands by delegating to existing scripts or RAG testing."""
    print_header("Testing Operations")
    
    if args.test_command == 'system':
        print_info(f"Running system integration tests in {args.mode} mode...")
        script_args = ['--mode', args.mode]
        return run_script('system_integration_tests', script_args)
    
    elif args.test_command == 'rag':
        print_info(f"Running RAG pipeline tests in {args.mode} mode...")
        # This will call our new RAG testing script
        script_args = ['--mode', args.mode]
        
        if args.query:
            script_args.extend(['--query', args.query])
        if args.collection:
            script_args.extend(['--collection', args.collection])
        if args.verbose:
            script_args.append('--verbose')
        
        return run_script('test_rag_pipeline', script_args)
    
    else:
        print("❌ No test command specified. Use --help for options.")
        return 1
