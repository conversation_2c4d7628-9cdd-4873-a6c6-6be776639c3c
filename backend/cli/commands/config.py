"""
Configuration CLI Commands

Provides configuration operations by delegating to existing scripts.
"""

import argparse
from ..utils import run_script, print_header, print_info


def setup_config_parser(subparsers):
    """Set up config command parser."""
    config_parser = subparsers.add_parser('config', help='Configuration operations')
    config_subparsers = config_parser.add_subparsers(dest='config_command', help='Configuration commands')
    
    # Validate command
    validate_parser = config_subparsers.add_parser('validate', help='Validate configuration')
    validate_parser.add_argument('--verbose', action='store_true', help='Verbose output')
    
    # Show command
    show_parser = config_subparsers.add_parser('show', help='Show current configuration')
    show_parser.add_argument('--section', help='Show specific configuration section')


def handle_config_command(args):
    """Handle configuration commands by delegating to existing scripts."""
    print_header("Configuration Operations")
    
    if args.config_command == 'validate':
        print_info("Validating configuration...")
        script_args = []
        if args.verbose:
            script_args.append('--verbose')
        
        return run_script('validate_config', script_args)
    
    elif args.config_command == 'show':
        print_info("Showing configuration...")
        script_args = ['--show']
        if args.section:
            script_args.extend(['--section', args.section])
        
        return run_script('validate_config', script_args)
    
    else:
        print("❌ No config command specified. Use --help for options.")
        return 1
