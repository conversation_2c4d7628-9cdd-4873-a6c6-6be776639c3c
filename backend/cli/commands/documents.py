"""
Document CLI Commands

Provides document operations by delegating to existing scripts.
"""

import argparse
from ..utils import run_script, print_header, print_info


def setup_documents_parser(subparsers):
    """Set up documents command parser."""
    docs_parser = subparsers.add_parser('documents', help='Document operations')
    docs_subparsers = docs_parser.add_subparsers(dest='docs_command', help='Document commands')
    
    # Load command
    load_parser = docs_subparsers.add_parser('load', help='Load documents into knowledge base')
    load_parser.add_argument('--source', required=True, help='Source directory or file path')
    load_parser.add_argument('--collection', help='Collection name (optional)')
    load_parser.add_argument('--chunk-size', type=int, help='Chunk size for document splitting')
    load_parser.add_argument('--chunk-overlap', type=int, help='Chunk overlap for document splitting')
    load_parser.add_argument('--force', action='store_true', help='Force reload of existing documents')


def handle_documents_command(args):
    """Handle document commands by delegating to existing scripts."""
    print_header("Document Operations")
    
    if args.docs_command == 'load':
        print_info(f"Loading documents from: {args.source}")
        script_args = ['--source', args.source]
        
        if args.collection:
            script_args.extend(['--collection', args.collection])
        if args.chunk_size:
            script_args.extend(['--chunk-size', str(args.chunk_size)])
        if args.chunk_overlap:
            script_args.extend(['--chunk-overlap', str(args.chunk_overlap)])
        if args.force:
            script_args.append('--force')
        
        return run_script('load_knowledge_base_documents', script_args)
    
    else:
        print("❌ No document command specified. Use --help for options.")
        return 1
