"""
Database CLI Commands

Provides database operations by delegating to existing scripts.
"""

import argparse
from ..utils import run_script, print_header, print_info


def setup_database_parser(subparsers):
    """Set up database command parser."""
    db_parser = subparsers.add_parser('database', help='Database operations')
    db_subparsers = db_parser.add_subparsers(dest='db_command', help='Database commands')

    # Setup command
    setup_parser = db_subparsers.add_parser('setup', help='Setup PostgreSQL database')
    setup_parser.add_argument('--force', action='store_true', help='Force recreation of database')
    setup_parser.add_argument('--use-migrations', action='store_true', help='Use Alembic migrations')

    # Migrate command
    migrate_parser = db_subparsers.add_parser('migrate', help='Run database migrations')
    migrate_parser.add_argument('action', choices=['upgrade', 'downgrade', 'status', 'history'],
                               help='Migration action')
    migrate_parser.add_argument('--revision', help='Target revision for upgrade/downgrade')

    # Status command
    db_subparsers.add_parser('status', help='Check database status')


def handle_database_command(args):
    """Handle database commands by delegating to existing scripts."""
    print_header("Database Operations")

    if args.db_command == 'setup':
        print_info("Setting up PostgreSQL database with pgvector...")
        script_args = []
        if args.force:
            script_args.append('--force')
        if args.use_migrations:
            script_args.append('--use-migrations')

        return run_script('setup_pgvector_db', script_args)

    elif args.db_command == 'migrate':
        print_info(f"Running database migration: {args.action}")
        # Map CLI actions to script commands
        action_mapping = {
            'upgrade': 'migrate',
            'downgrade': 'rollback',
            'status': 'status',
            'history': 'status'
        }
        script_command = action_mapping.get(args.action, args.action)
        script_args = [script_command]
        if args.revision:
            script_args.extend(['--revision', args.revision])

        return run_script('database_migrations', script_args)

    elif args.db_command == 'status':
        print_info("Checking database status...")
        return run_script('database_migrations', ['status'])

    else:
        print("❌ No database command specified. Use --help for options.")
        return 1
