#!/usr/bin/env python3
"""
BusinessLM CLI

Unified command-line interface for BusinessLM orchestration system.
Provides access to database, document, testing, and configuration operations.
"""

import sys
import argparse
from pathlib import Path

# Add backend to path for imports
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

from cli.commands.database import setup_database_parser, handle_database_command
from cli.commands.documents import setup_documents_parser, handle_documents_command
from cli.commands.test import setup_test_parser, handle_test_command
from cli.commands.config import setup_config_parser, handle_config_command
from cli.utils import print_header, print_error


def create_parser():
    """Create the main CLI parser."""
    parser = argparse.ArgumentParser(
        description='BusinessLM Orchestration CLI',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s database setup --create-db
  %(prog)s database migrate upgrade
  %(prog)s documents load --source ./docs
  %(prog)s test rag --mode full
  %(prog)s test system --mode clean
  %(prog)s config validate
        """
    )
    
    parser.add_argument('--version', action='version', version='BusinessLM CLI 1.0.0')
    
    # Create subparsers for main commands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Set up command parsers
    setup_database_parser(subparsers)
    setup_documents_parser(subparsers)
    setup_test_parser(subparsers)
    setup_config_parser(subparsers)
    
    return parser


def main():
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        print_header("BusinessLM Orchestration CLI")
        parser.print_help()
        return 0
    
    try:
        # Route to appropriate command handler
        if args.command == 'database':
            return handle_database_command(args)
        elif args.command == 'documents':
            return handle_documents_command(args)
        elif args.command == 'test':
            return handle_test_command(args)
        elif args.command == 'config':
            return handle_config_command(args)
        else:
            print_error(f"Unknown command: {args.command}")
            return 1
            
    except KeyboardInterrupt:
        print_error("Operation cancelled by user")
        return 1
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
