"""
CLI Utilities

Common utilities for CLI commands to avoid duplication.
"""

import sys
import subprocess
from pathlib import Path
from typing import List, Optional


def run_script(script_name: str, args: List[str] = None, cwd: Optional[Path] = None) -> int:
    """
    Run a script from the scripts directory with given arguments.
    
    Args:
        script_name: Name of the script (without .py extension)
        args: List of arguments to pass to the script
        cwd: Working directory (defaults to backend/)
        
    Returns:
        Exit code from the script
    """
    if args is None:
        args = []
    
    if cwd is None:
        # Default to backend directory
        cwd = Path(__file__).parent.parent
    
    script_path = cwd / "scripts" / f"{script_name}.py"
    
    if not script_path.exists():
        print(f"❌ Script not found: {script_path}")
        return 1
    
    # Build command
    cmd = [sys.executable, str(script_path)] + args
    
    try:
        # Run the script and return its exit code
        result = subprocess.run(cmd, cwd=cwd)
        return result.returncode
    except Exception as e:
        print(f"❌ Error running script {script_name}: {e}")
        return 1


def print_header(title: str) -> None:
    """Print a formatted header for CLI sections."""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)


def print_success(message: str) -> None:
    """Print a success message."""
    print(f"✅ {message}")


def print_error(message: str) -> None:
    """Print an error message."""
    print(f"❌ {message}")


def print_info(message: str) -> None:
    """Print an info message."""
    print(f"ℹ️  {message}")
