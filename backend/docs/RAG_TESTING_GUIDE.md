# RAG Testing Guide

## Overview

This guide provides comprehensive instructions for testing the RAG (Retrieval-Augmented Generation) components in the BusinessLM system. The testing infrastructure is designed to validate core functionality without duplications and supports both quick validation and comprehensive testing.

## Prerequisites

### 1. Database Setup
Ensure PostgreSQL with pgvector extension is properly configured:

```bash
# Setup database with pgvector
uv run python cli/main.py database setup

# Verify database status
uv run python cli/main.py database status
```

### 2. Environment Configuration
Ensure at least one LLM API key is configured in your `.env` file:

```bash
# Validate configuration
uv run python cli/main.py config validate
```

## Testing Methods

### 1. CLI-Based Testing (Recommended)

#### Quick RAG Test
Tests core components (embeddings, database, LLM) without end-to-end pipeline:

```bash
uv run python cli/main.py test rag --mode quick --verbose
```

#### Full RAG Test
Includes end-to-end pipeline testing with a default query:

```bash
uv run python cli/main.py test rag --mode full --verbose
```

#### Component-Specific Tests

**Embeddings Only:**
```bash
uv run python cli/main.py test rag --mode embeddings --verbose
```

**Database Search Only:**
```bash
uv run python cli/main.py test rag --mode search --verbose
```

**LLM Integration Only:**
```bash
uv run python cli/main.py test rag --mode llm --verbose
```

#### Custom Query Testing
Test with your own query:

```bash
uv run python cli/main.py test rag --mode full --query "What is machine learning?" --verbose
```

### 2. Direct Script Testing

For advanced testing or debugging:

```bash
uv run python scripts/test_rag_pipeline.py --mode full --verbose
```

## Test Components

### 1. Embedding Generation Test
- **Purpose**: Validates embedding model creation and text embedding generation
- **Checks**: 
  - Embedding factory initialization
  - Text-to-vector conversion
  - Embedding dimension validation
- **Expected Output**: Embedding vector with correct dimensions

### 2. Database Search Test
- **Purpose**: Validates database connectivity and document storage/retrieval
- **Checks**:
  - Database connection
  - Document and chunk counts
  - Basic query functionality
- **Expected Output**: Database statistics and successful queries

### 3. LLM Integration Test
- **Purpose**: Validates LLM adapter creation and text completion
- **Checks**:
  - LLM factory initialization
  - Simple completion request
  - Response validation
- **Expected Output**: Valid LLM response to test prompt

### 4. End-to-End Pipeline Test
- **Purpose**: Validates complete RAG workflow
- **Checks**:
  - Query embedding generation
  - Document retrieval from database
  - Context-aware LLM response generation
- **Expected Output**: Contextual response based on retrieved documents

## Test Modes

### Quick Mode (`--mode quick`)
- Tests: Embeddings + Database + LLM
- Duration: ~30-60 seconds
- Use Case: Regular validation, CI/CD

### Full Mode (`--mode full`)
- Tests: All components + End-to-End pipeline
- Duration: ~1-3 minutes
- Use Case: Comprehensive validation before deployment

### Component Modes
- `--mode embeddings`: Embedding generation only
- `--mode search`: Database search only  
- `--mode llm`: LLM integration only

## Expected Output

### Successful Test Output
```
🚀 Starting RAG Pipeline Tests
============================================================
🧪 Testing Embedding Generation...
✅ Generated embedding with 384 dimensions
✅ Embedding generation test passed

🔍 Testing Database Search...
📊 Database contains 0 documents and 0 chunks
✅ Database search test passed

🤖 Testing LLM Integration...
✅ LLM response: 2+2 equals 4...
✅ LLM integration test passed

============================================================
Embeddings: ✅ PASSED
Database Search: ✅ PASSED
LLM Integration: ✅ PASSED
🎉 All 3 RAG tests passed!
✅ RAG pipeline is working correctly
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Failed
```bash
# Check PostgreSQL service
brew services list | grep postgresql

# Restart if needed
brew services restart postgresql@15

# Verify pgvector extension
psql -d postgres -c "SELECT * FROM pg_extension WHERE extname = 'vector';"
```

#### 2. Missing API Keys
```bash
# Validate configuration
uv run python cli/main.py config validate

# Check .env file for required keys
cat .env | grep -E "(OPENAI|ANTHROPIC|GOOGLE)_API_KEY"
```

#### 3. Import Errors
```bash
# Install missing dependencies
uv add sentence-transformers openai anthropic google-generativeai

# Verify installation
uv run python -c "import sentence_transformers; print('OK')"
```

#### 4. Slow Embedding Tests
First-time embedding model downloads can be slow. Subsequent runs will be faster due to caching.

## Integration with Migration

### Before Day 3+ Migration
Run comprehensive tests to ensure RAG foundation is solid:

```bash
# Full validation
uv run python cli/main.py test rag --mode full --verbose
uv run python cli/main.py test system --mode clean
```

### Future Multi-Agent Testing
The testing infrastructure is designed to support future multi-agent orchestration testing:

```bash
# Future capability (not yet implemented)
uv run python cli/main.py test orchestration --mode multi-agent --observe
```

## Performance Considerations

- **First Run**: May take 2-5 minutes due to model downloads
- **Subsequent Runs**: Should complete in 30-60 seconds
- **Memory Usage**: ~2-4GB for embedding models
- **Disk Space**: ~1-2GB for cached models

## Best Practices

1. **Regular Testing**: Run quick tests after configuration changes
2. **Full Testing**: Run comprehensive tests before major deployments
3. **Clean Environment**: Use `--mode clean` for production-like testing
4. **Verbose Output**: Use `--verbose` for debugging and detailed validation
5. **Component Isolation**: Use component-specific modes to isolate issues
