from typing import List, Dict, Any
from .state import Task, TaskStatus, TaskType

def generate_plan(user_input: str, context: Dict[str, Any]) -> List[Task]:
    """Convert user input into a list of executable tasks."""

    # Mock task creation
    task1 = Task(
        id="task-1",
        description="Analyze the user's query and break it down",
        task_type=TaskType.QUERY_DECOMPOSITION,
        status=TaskStatus.PENDING,
        dependencies=set()
    )

    task2 = Task(
        id="task-2",
        description="Perform reasoning on the decomposed sub-questions",
        task_type=TaskType.REASONING,
        status=TaskStatus.PENDING,
        dependencies={"task-1"}
    )

    task3 = Task(
        id="task-3",
        description="Generate a response for the user",
        task_type=TaskType.ACTION,
        status=TaskStatus.PENDING,
        dependencies={"task-2"}
    )

    return [task1, task2, task3]