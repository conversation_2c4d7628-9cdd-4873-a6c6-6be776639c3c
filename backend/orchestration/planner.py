"""
Focuses on task decomposition and assigning tasks to agents:

- Accepts `user_input` from `LangGraphState` and outputs a structured list of `Task` instances.
- Responsible for:
  -> Breaking down the query into discrete subtasks.
  -> Setting task dependencies and types.
- Output:
  -> Populates `state.tasks` with Task objects.
  -> Sets `state.current_step` to `TASK_ASSIGNMENT`.
"""

import logging
import json
import uuid  # For generating unique task IDs
from typing import List, Dict, Any, Optional
from datetime import datetime

# Import orchestration components
from .state import Task, TaskStatus, TaskType

# Import LLM components
from ..llm import get_llm_adapter, LLMAdapter

# Import RAG components
from ..rag import initialize_knowledge_base_service, KnowledgeBaseService

# Import configuration
from ..app.config import get_settings
from ..rag.utils import RAGConfig

logger = logging.getLogger(__name__)


class OrchestrationPlanner:
    """
    Orchestration planner that uses LLM reasoning and RAG context
    to generate structured task plans for multi-agent orchestration.

    LLM & RAG integrations:
    -> Use LLM for query analysis and task decomposition
    -> Leverage RAG for user-specific context
    -> Generate structured `Task` objects with `dependencies` and `types` methods
    """

    def __init__(
        self,
        llm_adapter: Optional[LLMAdapter] = None,
        knowledge_base_service: Optional[KnowledgeBaseService] = None,
        max_context_docs: int = 5,  # Max docs retrieved -> can and should be adjusted
        planning_temperature: float = 0.3,  # Lower temperature for deterministic planning
        # planning_max_tokens: int = 1000,  # Max tokens for planning response
    ):
        """
        Initialize the orchestration planner.

        Args:

        - llm_adapter: LLM adapter for reasoning (auto-initialized if None)
        - knowledge_base_service: RAG service for context (auto-initialized if None)
        - max_context_docs: Maximum documents to retrieve for context
        - planning_temperature: LLM temperature for planning (lower = more structured)
        # planning_max_tokens: Max tokens for planning response
        """
        # Store configuration
        self.max_context_docs = max_context_docs
        self.planning_temperature = planning_temperature
        # self.planning_max_tokens = planning_max_tokens
        self.settings = get_settings()

        # Initialize LLM & RAG services
        self.llm_adapter = llm_adapter
        self.knowledge_base_service = knowledge_base_service

        # Planning prompt templates
        self._planning_prompts = self._initialize_planning_prompts()

    async def generate_plan(
        self, user_input: str, context: Optional[Dict[str, Any]] = None
    ) -> List[Task]:
        """
        Generate a structured task plan based on user input and context.

        This is the main orchestration method that:
        1. Gathers relevant context from RAG
        2. Analyzes user intent with LLM
        3. Generates structured tasks with dependencies
        4. Validates and returns the plan

        Args:
            user_input: The raw user query
            context: Additional context (previous tasks, user preferences, etc.)

        Returns:
            List of Task objects representing the execution plan
        """
        if context is None:
            context = {}

        start_time = datetime.now()
        logger.info(f"Starting plan generation for: {user_input[:100]}...")

        try:
            # Step 1: Initialize services if needed
            await self._initialize_services()

            # Step 2: Gather RAG context
            logger.debug("Gathering RAG context for planning")
            rag_context = await self._gather_rag_context(user_input, context)

            # Step 3: Generate tasks using LLM reasoning
            logger.debug("Generating tasks with LLM reasoning")
            tasks = await self._generate_tasks_with_llm(user_input, rag_context, context)

            # Step 4: Enrich tasks with additional context
            enriched_tasks = await self._enrich_tasks_with_context(
                tasks, rag_context, context
            )

            # Step 5: Log planning metrics
            planning_time = (datetime.now() - start_time).total_seconds()
            logger.info(
                f"Plan generation completed: {len(enriched_tasks)} tasks in {planning_time:.2f}s"
            )

            return enriched_tasks

        except Exception as e:
            logger.error(f"Error in plan generation: {e}")
            # Return fallback plan to ensure system continues functioning
            return self._generate_fallback_tasks(user_input, context)

    async def _initialize_services(self):
        """
        Initialize LLM and RAG services with proper error handling and fallbacks.

        This method handles:
        - Service factory instantiation
        - Configuration loading
        - Error handling and fallbacks
        - Development vs production mode differences
        """

        try:
            # Initialize LLM adapter if not provided
            if self.llm_adapter is None:
                logger.info("Initializing LLM adapter for orchestration planning")

                # Determine provider based on available API keys
                llm_provider = self._determine_llm_provider()

                self.llm_adapter = get_llm_adapter(
                    provider=llm_provider,
                    fallback=True,  # Enable fallback to other providers
                    temperature=self.planning_temperature,
                    max_tokens=2000,  # Sufficient for task planning
                    api_key=self._get_api_key_for_provider(llm_provider),
                )

                logger.info(f"LLM adapter initialized: {llm_provider}")

            # Initialize RAG knowledge base service if not provided
            if self.knowledge_base_service is None:
                logger.info("Initializing RAG knowledge base service")

                # Create RAG configuration
                rag_config = RAGConfig(
                    embedding_provider=self.settings.DEFAULT_EMBEDDING_MODEL,
                    embedding_model=self.settings.EMBEDDING_MODEL,
                    vector_store_type=self.settings.VECTOR_STORE_TYPE,
                    vector_weight=0.7,  # Favor vector search for planning context
                    keyword_weight=0.3,
                    use_reranking=True,  # Enable reranking for better context
                )

                self.knowledge_base_service = await initialize_knowledge_base_service(
                    config=rag_config
                )

                logger.info("RAG knowledge base service initialized")

        except Exception as e:
            logger.error(f"Error initializing orchestration planner services: {e}")
            # Should we raise here or use mock services? Let's discuss this decision point.
            raise


    def _determine_llm_provider(self) -> str:
        """Determine the best available LLM provider based on API keys."""
        if self.settings.OPENAI_API_KEY:
            return "openai"
        elif self.settings.ANTHROPIC_API_KEY:
            return "anthropic"
        elif self.settings.GOOGLE_API_KEY:
            return "gemini"
        else:
            logger.warning("No LLM API keys found, falling back to mock adapter")
            return "mock"


    def _get_api_key_for_provider(self, provider: str) -> Optional[str]:
        """Get the appropriate API key for the given provider."""
        key_mapping = {
            "openai": self.settings.OPENAI_API_KEY,
            "anthropic": self.settings.ANTHROPIC_API_KEY,
            "gemini": self.settings.GOOGLE_API_KEY,
        }
        return key_mapping.get(provider)


    def _initialize_planning_prompts(self) -> Dict[str, str]:
        """
        Initialize prompt templates for different planning scenarios.

        These prompts guide the LLM to generate structured, actionable task plans
        that align with our orchestration architecture.

        Returns:
            Dictionary of prompt templates keyed by scenario name
        """
        prompts = {
            "task_analysis": """
    You are an expert business orchestration planner. Analyze the user's request and break it down into structured tasks.

    USER REQUEST: {user_input}

    AVAILABLE CONTEXT:
    {context_summary}

    RELEVANT KNOWLEDGE:
    {rag_context}

    Your task is to create a structured plan with the following task types:
    - QUERY_DECOMPOSITION: Break down complex queries into sub-questions
    - REASONING: Analyze information and draw conclusions  
    - COMMUNICATION: Coordinate between different agents or stakeholders
    - TOOLS: Use specific tools (web search, document retrieval, calculations)
    - ACTION: Execute specific business actions
    - EVALUATION: Review and validate results

    REQUIREMENTS:
    1. Each task must have a clear, actionable description
    2. Identify dependencies between tasks (which tasks must complete before others)
    3. Assign appropriate task types
    4. Consider the business context and domain knowledge provided

    OUTPUT FORMAT (JSON):
    {{
        "analysis": "Brief analysis of the user's request and approach",
        "tasks": [
            {{
                "description": "Clear, actionable task description",
                "task_type": "TASK_TYPE_FROM_LIST_ABOVE",
                "dependencies": ["list", "of", "task", "ids", "this", "depends", "on"],
                "estimated_complexity": "low|medium|high",
                "assigned_agent_suggestion": "co_ceo|finance|marketing|null"
            }}
        ]
    }}

    Generate a comprehensive but efficient plan:""",
            "context_summary": """
    Summarize the following context for planning purposes:

    PREVIOUS TASKS: {previous_tasks}
    USER PREFERENCES: {user_preferences}  
    CURRENT SESSION: {session_context}

    Provide a brief summary of relevant context that should influence task planning:""",
            "task_validation": """
    Review this task plan for logical consistency and completeness:

    PLAN: {task_plan}
    ORIGINAL REQUEST: {user_input}

    Check for:
    1. Missing critical steps
    2. Circular dependencies
    3. Appropriate task sequencing
    4. Resource requirements

    Provide validation feedback and suggest improvements:""",
        }

        return prompts


    async def _gather_rag_context(
        self, user_input: str, context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Gather relevant context from the knowledge base to inform planning.

        This method:
        1. Searches for relevant documents using hybrid search
        2. Extracts key insights and domain knowledge
        3. Identifies potential complexity factors
        4. Suggests relevant business domains/agents

        Args:
            user_input: The user's query
            context: Additional context that might inform search

        Returns:
            Dictionary containing RAG context and insights
        """
        rag_context = {
            "relevant_documents": [],
            "domain_insights": [],
            "complexity_indicators": [],
            "suggested_agents": [],
            "search_metadata": {},
        }

        try:
            if self.knowledge_base_service is None:
                logger.warning("Knowledge base service not available, skipping RAG context")
                return rag_context

            # Perform hybrid search for relevant documents
            logger.debug(f"Searching knowledge base for: {user_input}")

            search_results = await self.knowledge_base_service.search(
                query=user_input,
                limit=self.max_context_docs,
                search_type="hybrid",  # Use both vector and keyword search
                filters=context.get("search_filters", {}) if context else {},
            )

            if search_results:
                # Extract document content and metadata
                for result in search_results:
                    doc_context = {
                        "content": result.get("content", ""),
                        "metadata": result.get("metadata", {}),
                        "relevance_score": result.get("score", 0.0),
                        "source": result.get("source", "unknown"),
                    }
                    rag_context["relevant_documents"].append(doc_context)

                # Analyze documents for planning insights
                rag_context.update(
                    await self._analyze_rag_documents(search_results, user_input)
                )

                logger.info(
                    f"Retrieved {len(search_results)} relevant documents for planning context"
                )
            else:
                logger.info("No relevant documents found in knowledge base")

        except Exception as e:
            logger.error(f"Error gathering RAG context: {e}")
            # Continue with empty context rather than failing

        return rag_context


    async def _analyze_rag_documents(
        self, documents: List[Dict[str, Any]], user_input: str
    ) -> Dict[str, Any]:
        """
        Analyze retrieved documents to extract planning insights.

        This method identifies:
        - Domain-specific terminology and concepts
        - Complexity indicators (regulatory requirements, multi-step processes)
        - Suggested business domains/agents
        - Key considerations for task planning
        """
        analysis = {
            "domain_insights": [],
            "complexity_indicators": [],
            "suggested_agents": [],
        }

        # Extract metadata-based insights
        for doc in documents:
            metadata = doc.get("metadata", {})

            # Identify business domains
            if "department" in metadata:
                dept = metadata["department"].lower()
                if dept in ["finance", "financial"]:
                    analysis["suggested_agents"].append("finance")
                elif dept in ["marketing", "sales"]:
                    analysis["suggested_agents"].append("marketing")

            # Identify complexity indicators
            content = doc.get("content", "").lower()
            if any(term in content for term in ["regulation", "compliance", "legal"]):
                analysis["complexity_indicators"].append("regulatory_requirements")
            if any(term in content for term in ["multi-step", "process", "workflow"]):
                analysis["complexity_indicators"].append("multi_step_process")
            if any(term in content for term in ["budget", "financial", "cost"]):
                analysis["complexity_indicators"].append("financial_analysis_required")

        # Remove duplicates
        analysis["suggested_agents"] = list(set(analysis["suggested_agents"]))
        analysis["complexity_indicators"] = list(set(analysis["complexity_indicators"]))

        return analysis


    async def _generate_tasks_with_llm(
        self, user_input: str, rag_context: Dict[str, Any], context: Optional[Dict[str, Any]] = None
    ) -> List[Task]:
        """
        Use LLM reasoning to generate structured tasks based on user input and context.

        This method:
        1. Prepares context-rich prompts
        2. Calls LLM for task analysis and generation
        3. Parses and validates LLM response
        4. Creates Task objects with proper structure

        Args:
            user_input: The user's query
            rag_context: Context gathered from RAG system
            context: Additional orchestration context

        Returns:
            List of structured Task objects
        """
        try:
            # Prepare context summary for LLM
            context_summary = self._prepare_context_summary(context)
            rag_summary = self._prepare_rag_summary(rag_context)

            # Build the planning prompt
            planning_prompt = self._planning_prompts["task_analysis"].format(
                user_input=user_input,
                context_summary=context_summary,
                rag_context=rag_summary,
            )

            # Call LLM for task planning
            logger.debug("Calling LLM for task planning analysis")

            messages = [
                {"role": "system", "content": "You are an expert business orchestration planner."},
                {"role": "user", "content": planning_prompt},
            ]

            llm_response = await self.llm_adapter.chat(
                messages=messages, temperature=self.planning_temperature, max_tokens=2000
            )

            logger.debug(f"LLM planning response received: {len(llm_response)} characters")

            # Parse LLM response into structured tasks
            tasks = await self._parse_llm_response_to_tasks(llm_response, user_input)

            logger.info(f"Generated {len(tasks)} tasks from LLM planning")
            return tasks

        except Exception as e:
            logger.error(f"Error generating tasks with LLM: {e}")
            # Fallback to simple task generation
            return self._generate_fallback_tasks(user_input, context)


    def _prepare_context_summary(self, context: Dict[str, Any]) -> str:
        """Prepare a human-readable context summary for LLM prompting."""
        if not context:
            return "No additional context provided."

        summary_parts = []

        if "previous_tasks" in context:
            summary_parts.append(
                f"Previous tasks: {len(context['previous_tasks'])} completed"
            )

        if "user_preferences" in context:
            prefs = context["user_preferences"]
            summary_parts.append(f"User preferences: {', '.join(prefs.keys())}")

        if "session_context" in context:
            summary_parts.append(f"Session context: {context['session_context']}")

        return "; ".join(summary_parts) if summary_parts else "No additional context."


    def _prepare_rag_summary(self, rag_context: Dict[str, Any]) -> str:
        """Prepare a summary of RAG context for LLM prompting."""
        if not rag_context.get("relevant_documents"):
            return "No relevant knowledge base documents found."

        summary_parts = []

        # Summarize document content
        docs = rag_context["relevant_documents"]
        summary_parts.append(f"Found {len(docs)} relevant documents")

        # Add key insights
        if rag_context.get("domain_insights"):
            summary_parts.append(
                f"Domain insights: {', '.join(rag_context['domain_insights'])}"
            )

        if rag_context.get("complexity_indicators"):
            summary_parts.append(
                f"Complexity factors: {', '.join(rag_context['complexity_indicators'])}"
            )

        if rag_context.get("suggested_agents"):
            summary_parts.append(
                f"Suggested agents: {', '.join(rag_context['suggested_agents'])}"
            )

        # Include top document excerpts
        top_docs = docs[:2]  # Include top 2 documents
        for i, doc in enumerate(top_docs, 1):
            content = doc.get("content", "")[:200]  # First 200 chars
            summary_parts.append(f"Document {i}: {content}...")

        return "\n".join(summary_parts)


    async def _parse_llm_response_to_tasks(
        self, llm_response: str, user_input: str
    ) -> List[Task]:
        """
        Parse LLM response into structured Task objects.

        This method handles:
        1. JSON extraction from LLM response
        2. Task object creation with unique IDs
        3. Dependency mapping and validation
        4. Error handling and fallbacks

        Args:
            llm_response: Raw response from LLM
            user_input: Original user input for fallback context

        Returns:
            List of validated Task objects
        """
        try:
            # Extract JSON from LLM response
            task_data = self._extract_json_from_response(llm_response)

            if not task_data or "tasks" not in task_data:
                logger.warning("No valid task data found in LLM response")
                return self._generate_fallback_tasks(user_input, {})

            # Create Task objects with unique IDs
            tasks = []
            task_id_mapping = {}  # Map from description-based IDs to UUIDs

            # First pass: Create tasks with temporary IDs
            for i, task_spec in enumerate(task_data["tasks"]):
                task_id = f"task-{uuid.uuid4().hex[:8]}"
                temp_id = f"task-{i+1}"  # Temporary ID for dependency mapping

                task = Task(
                    id=task_id,
                    description=task_spec.get("description", f"Task {i+1}"),
                    task_type=self._parse_task_type(task_spec.get("task_type", "ACTION")),
                    status=TaskStatus.PENDING,
                    dependencies=set(),  # Will be populated in second pass
                    assigned_agent=task_spec.get("assigned_agent_suggestion"),
                )

                tasks.append(task)
                task_id_mapping[temp_id] = task_id

            # Second pass: Map dependencies
            for i, (task, task_spec) in enumerate(zip(tasks, task_data["tasks"])):
                dependencies = task_spec.get("dependencies", [])
                mapped_dependencies = set()

                for dep in dependencies:
                    if dep in task_id_mapping:
                        mapped_dependencies.add(task_id_mapping[dep])
                    else:
                        logger.warning(f"Invalid dependency reference: {dep}")

                task.dependencies = mapped_dependencies

            # Validate task plan
            if self._validate_task_plan(tasks):
                logger.info(f"Successfully parsed {len(tasks)} tasks from LLM response")
                return tasks
            else:
                logger.warning("Task plan validation failed, using fallback")
                return self._generate_fallback_tasks(user_input, {})

        except Exception as e:
            logger.error(f"Error parsing LLM response to tasks: {e}")
            return self._generate_fallback_tasks(user_input, {})


    def _extract_json_from_response(self, response: str) -> Optional[Dict[str, Any]]:
        """Extract JSON data from LLM response, handling various formats."""
        try:
            # Try direct JSON parsing first
            return json.loads(response)
        except json.JSONDecodeError:
            # Try to find JSON within the response
            import re

            json_pattern = r"\{.*\}"
            matches = re.findall(json_pattern, response, re.DOTALL)

            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue

            logger.warning("Could not extract valid JSON from LLM response")
            return None


    def _parse_task_type(self, task_type_str: str) -> TaskType:
        """Parse task type string to TaskType enum with fallback."""
        try:
            return TaskType(task_type_str.upper())
        except ValueError:
            logger.warning(f"Unknown task type: {task_type_str}, defaulting to ACTION")
            return TaskType.ACTION


    def _validate_task_plan(self, tasks: List[Task]) -> bool:
        """
        Validate the generated task plan for logical consistency.

        Checks for:
        - Circular dependencies
        - Valid task references
        - Reasonable task count
        """
        if not tasks or len(tasks) > 20:  # Reasonable limits
            return False

        # Check for circular dependencies using topological sort
        task_ids = {task.id for task in tasks}

        # Verify all dependencies reference valid tasks
        for task in tasks:
            if not task.dependencies.issubset(task_ids):
                logger.warning(f"Task {task.id} has invalid dependencies")
                return False

        # Simple cycle detection (could be more sophisticated)
        visited = set()
        rec_stack = set()

        def has_cycle(task_id: str) -> bool:
            if task_id in rec_stack:
                return True
            if task_id in visited:
                return False

            visited.add(task_id)
            rec_stack.add(task_id)

            # Find task and check its dependencies
            task = next((t for t in tasks if t.id == task_id), None)
            if task:
                for dep_id in task.dependencies:
                    if has_cycle(dep_id):
                        return True

            rec_stack.remove(task_id)
            return False

        # Check each task for cycles
        for task in tasks:
            if task.id not in visited:
                if has_cycle(task.id):
                    logger.warning("Circular dependency detected in task plan")
                    return False

        return True


    def _generate_fallback_tasks(
        self, user_input: str, context: Optional[Dict[str, Any]] = None
    ) -> List[Task]:
        """Generate a simple fallback task plan when LLM parsing fails."""
        logger.info("Generating fallback task plan")

        return [
            Task(
                id=f"fallback-{uuid.uuid4().hex[:8]}",
                description=f"Analyze and respond to: {user_input}",
                task_type=TaskType.REASONING,
                status=TaskStatus.PENDING,
                dependencies=set(),
                assigned_agent="co_ceo",
            )
        ]




    async def _enrich_tasks_with_context(
        self, tasks: List[Task], rag_context: Dict[str, Any], context: Optional[Dict[str, Any]] = None
    ) -> List[Task]:
        """
        Enrich generated tasks with additional context and metadata.

        This method adds:
        - RAG context references
        - Complexity estimates
        - Agent assignment refinements
        - Execution hints
        """
        enriched_tasks = []

        for task in tasks:
            # Create a copy to avoid modifying original
            enriched_task = Task(
                id=task.id,
                description=task.description,
                task_type=task.task_type,
                status=task.status,
                dependencies=task.dependencies.copy(),
                assigned_agent=task.assigned_agent,
                output=task.output,
            )

            # Add context metadata (this could be stored in a separate metadata field)
            # For now, we'll enhance the description with context hints
            if rag_context.get("complexity_indicators"):
                complexity_note = (
                    f" [Complexity: {', '.join(rag_context['complexity_indicators'])}]"
                )
                enriched_task.description += complexity_note

            enriched_tasks.append(enriched_task)

        return enriched_tasks


# Global planner instance for backward compatibility
_global_planner: Optional[OrchestrationPlanner] = None


async def plan(user_input: str, context: Optional[Dict[str, Any]] = None) -> List[Task]:
    """
    Backward compatibility function for existing code.

    This function maintains the original API while using the enhanced planner internally.
    It creates a global planner instance on first use and reuses it for subsequent calls.

    Args:
        user_input: The user's query
        context: Additional context for planning

    Returns:
        List of Task objects representing the execution plan
    """
    global _global_planner

    # Initialize global planner on first use
    if _global_planner is None:
        logger.info("Initializing global orchestration planner")
        _global_planner = OrchestrationPlanner()

    # Use the enhanced planner
    return await _global_planner.generate_plan(user_input, context)


# Backward compatibility alias
async def generate_plan(user_input: str, context: Optional[Dict[str, Any]] = None) -> List[Task]:
    """Backward compatibility alias for plan()."""
    return await plan(user_input, context)
