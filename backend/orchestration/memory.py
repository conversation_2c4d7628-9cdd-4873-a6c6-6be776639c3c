"""
A shared memory interface to enrich context across steps.
- Feeds data into `state.context`, including:
  -> Historical task traces.
  -> RAG-retrieved relevant information.
  -> Agent/system memory.
- Used by planner and agents to reason and personalize behavior.
"""

from __future__ import annotations

import uuid  # For generating unique task IDs
from enum import Enum
from typing import Optional, List, Dict, Any, Union, Set # Type hints for optional values (Optional), multiple types (Union), and unique collections (Set)
from datetime import datetime, timezone

from pydantic import BaseModel, Field
from .state import OrchestrationStep, LangGraphState

import logging
logger = logging.getLogger(__name__)


class MemoryUnits(BaseModel):
    """Defines what encompasses memory. 
    I.e, models the unit of memory - a single event, reflection, observation or message
    considered worth remembering during orchestration."""
    memory_id: uuid.UUID = Field(default_factory=uuid.uuid4, description="unique identifier for each memory record")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    current_step: OrchestrationStep = Field(default_factory=OrchestrationStep, description="step in the orchestration process when this memory was generated")
    content: str = Field(default_factory=str, description="content of the event being recorded")
    agent: str = Field(default_factory=str, description="agent that generated this memory")
    task_id: str = Field(default_factory=str, description="task ID that generated this memory")
    metadata: Dict[str, Any] = Field(default_factory=dict)
    tags: List[str] = Field(default_factory=list, description="optional tags for filtering/searching memory")


class GlobalMemory:
    def __init__(self, first_memory: Optional[List[MemoryUnits]] = None):
        self.memory: List[MemoryUnits] = first_memory or []
        

    def add_memory(self, memory_unit: MemoryUnits):
        for existing in self.memory:
            if existing.memory_id == memory_unit.memory_id:
                return memory_unit.memory_id

        self.memory.append(memory_unit)
        logger.debug(f"[Memory] Added memory: {memory_unit.memory_id}, step={memory_unit.current_step}, agent={memory_unit.agent}, task={memory_unit.task_id}")
        return memory_unit.memory_id
    

    def get_relevant_memory(self, filters: Dict):
        pass
    

    def inject_into_context(self, state: LangGraphState) -> LangGraphState:
        pass