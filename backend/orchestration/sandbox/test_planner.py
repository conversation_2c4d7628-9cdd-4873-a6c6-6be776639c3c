from orchestration.planner import generate_plan

def main():
    # Sample test input
    user_query = "How do I launch a product?"
    context = {}  # Could include things like industry, budget, etc.

    # Run planner
    plan = generate_plan(user_query, context)

    # Display the generated plan
    print("Generated Task Plan:\n" + "-" * 25)
    for task in plan:
        print(f"[{task.id}] {task.description} → {task.task_type}")

if __name__ == "__main__":
    main()
