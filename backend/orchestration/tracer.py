"""ALL OF THIS IS BOILERPLATE AND SHOULD BE REVIEWED TO 
ACTUALLY BE INTEGRATED WITH THE CURRENT ORCHESTRATION FILES"""

"""CHECK Evals by LangWatch: https://www.linkedin.com/feed/update/urn:li:activity:7336001800015093761/"""

from typing import List, Optional, Tuple
from rich.console import Console
from rich.tree import Tree
from rich.theme import Theme
import json
from datetime import datetime

# -------- 🎨 Custom Rich Theme --------
theme = Theme({
    "agent": "bold cyan",
    "tool": "bold green",
    "task": "dim",
    "text": "white",
    "header": "bold magenta",
})
console = Console(theme=theme)

# -------- 🧠 TraceLogger Class --------
class TraceLogger:
    def __init__(self):
        self.trace: List[dict] = []
        self.tree_root = Tree("[header]Execution Trace")

    def log(self, agent: str, task: str, input_text: str, output_text: str, tool: Optional[str] = None):
        node_label = f"[agent]{agent}[/agent] - [task]{task}[/task]"
        if tool:
            node_label += f" → [tool]{tool}[/tool]"

        node = self.tree_root.add(node_label)
        node.add(f"[text]Input:[/text] {input_text.strip()}")
        node.add(f"[text]Output:[/text] {output_text.strip()}")

        self.trace.append({
            "timestamp": datetime.utcnow().isoformat(),
            "agent": agent,
            "task": task,
            "tool": tool,
            "input": input_text,
            "output": output_text,
        })

    def render(self):
        console.print(self.tree_root)

    def dump_json(self, path: str = "trace.json"):
        with open(path, "w") as f:
            json.dump(self.trace, f, indent=2)

    def from_state(self, state: dict):
        for event in state.get("trace", []):
            self.log(
                agent=event.get("agent", "Unknown"),
                task=event.get("task", "unknown"),
                input_text=event.get("input", ""),
                output_text=event.get("output", ""),
                tool=event.get("tool")
            )

# -------- 🧪 LangGraph Runner + Logger --------
def trace_from_graph(graph, input_state: dict, dump_to: Optional[str] = None) -> Tuple[dict, TraceLogger]:
    final_state, langgraph_trace = graph.collect().invoke(input_state)

    logger = TraceLogger()
    for event in langgraph_trace:
        logger.log(
            agent=event.get("name", "UnknownNode"),
            task=event.get("type", "unknown"),
            input_text=str(event.get("input", "")),
            output_text=str(event.get("output", "")),
            tool=event.get("tool_used")
        )

    if dump_to:
        logger.dump_json(dump_to)

    return final_state, logger
