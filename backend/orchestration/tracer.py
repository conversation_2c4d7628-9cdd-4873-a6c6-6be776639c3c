# Created while implementing state.py
# Review if class neds adjustment or full rewriting
from pydantic import BaseModel
from typing import Optional, Any
from .state import OrchestrationStep

class TraceEvent(BaseModel):
    """Captures key orchestration events for inspection and debugging."""
    step: OrchestrationStep
    agent: Optional[str] = None
    task_id: Optional[str] = None
    action: str  # e.g., "agent_called", "tool_invoked", "task_assigned"
    input: Optional[Any] = None
    output: Optional[Any] = None
    timestamp: Optional[str] = None  # ISO 8601 format


# Look into get.graph for graph observability!!
# https://python.langchain.com/docs/use_cases/observability
# Also look into Langsmith
