#!/usr/bin/env python3
"""
RAG Pipeline Testing Script

Tests core RAG functionality including embeddings, search, and LLM integration.
Focuses on core functionality without creating duplications.
"""

import argparse
import asyncio
from typing import List, Dict, Any, Optional

# Import script utilities
from script_utils import setup_script_environment, ScriptContext, script_main


def create_test_queries() -> List[Dict[str, Any]]:
    """Create a set of test queries for RAG pipeline testing."""
    return [
        {
            "query": "What is machine learning?",
            "expected_topics": ["machine learning", "AI", "algorithms"],
            "category": "general"
        },
        {
            "query": "How does neural network training work?",
            "expected_topics": ["neural networks", "training", "backpropagation"],
            "category": "technical"
        },
        {
            "query": "What are the benefits of cloud computing?",
            "expected_topics": ["cloud", "computing", "benefits", "scalability"],
            "category": "business"
        }
    ]


async def test_embeddings(logger, verbose: bool = False) -> bool:
    """Test embedding functionality."""
    logger.info("🧪 Testing Embedding Generation...")
    
    try:
        from rag.embeddings.factory import EmbeddingModelFactory
        
        # Test embedding factory
        embedding_model = EmbeddingModelFactory.create_model()
        if not embedding_model:
            logger.error("❌ Failed to create embedding model")
            return False
        
        # Test embedding generation
        test_text = "This is a test document for embedding generation."
        embedding = await embedding_model.embed_text(test_text)
        
        if embedding is None or len(embedding) == 0:
            logger.error("❌ Failed to generate embeddings")
            return False
        
        if verbose:
            logger.info(f"✅ Generated embedding with {len(embedding)} dimensions")
        
        logger.info("✅ Embedding generation test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Embedding test failed: {e}")
        return False


async def test_database_search(logger, verbose: bool = False, collection: Optional[str] = None) -> bool:
    """Test database search functionality."""
    logger.info("🔍 Testing Database Search...")
    
    try:
        from app.core.db.database import get_database_session
        from app.core.db.models import Document, DocumentChunk
        from sqlalchemy import func
        
        # Test database connection and basic queries
        with get_database_session() as session:
            # Check if we have any documents
            doc_count = session.query(func.count(Document.id)).scalar()
            chunk_count = session.query(func.count(DocumentChunk.id)).scalar()
            
            if verbose:
                logger.info(f"📊 Database contains {doc_count} documents and {chunk_count} chunks")
            
            if doc_count == 0:
                logger.warning("⚠️  No documents found in database. Consider loading test documents first.")
                return True  # Not a failure, just empty database
            
            # Test basic search query
            sample_chunks = session.query(DocumentChunk).limit(3).all()
            if sample_chunks:
                logger.info(f"✅ Successfully queried {len(sample_chunks)} document chunks")
            
        logger.info("✅ Database search test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database search test failed: {e}")
        return False


async def test_llm_integration(logger, verbose: bool = False) -> bool:
    """Test LLM integration functionality."""
    logger.info("🤖 Testing LLM Integration...")
    
    try:
        from llm.factory import LLMAdapterFactory
        
        # Test LLM factory
        llm_adapter = LLMAdapterFactory.create_adapter()
        if not llm_adapter:
            logger.error("❌ Failed to create LLM adapter")
            return False
        
        # Test simple completion
        test_prompt = "What is 2+2? Answer briefly."
        response = await llm_adapter.complete(test_prompt)
        
        if not response or len(response.strip()) == 0:
            logger.error("❌ LLM returned empty response")
            return False
        
        if verbose:
            logger.info(f"✅ LLM response: {response[:100]}...")
        
        logger.info("✅ LLM integration test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ LLM integration test failed: {e}")
        return False


async def test_rag_pipeline_end_to_end(logger, query: str, verbose: bool = False, collection: Optional[str] = None) -> bool:
    """Test complete RAG pipeline end-to-end."""
    logger.info(f"🔄 Testing RAG Pipeline End-to-End with query: '{query}'")
    
    try:
        # This would test the complete RAG pipeline
        # For now, we'll test the components individually
        
        # Test 1: Generate query embedding
        from rag.embeddings.factory import EmbeddingModelFactory
        embedding_model = EmbeddingModelFactory.create_model()
        query_embedding = await embedding_model.embed_text(query)
        
        if not query_embedding:
            logger.error("❌ Failed to generate query embedding")
            return False
        
        # Test 2: Search for relevant documents
        from app.core.db.database import get_database_session
        from app.core.db.models import DocumentChunk
        
        with get_database_session() as session:
            # Simple text search for now (vector search would require more setup)
            relevant_chunks = session.query(DocumentChunk).filter(
                DocumentChunk.content.ilike(f"%{query.split()[0]}%")
            ).limit(3).all()
            
            if verbose:
                logger.info(f"📄 Found {len(relevant_chunks)} potentially relevant chunks")
        
        # Test 3: Generate response with LLM
        from llm.factory import LLMAdapterFactory
        llm_adapter = LLMAdapterFactory.create_adapter()
        
        context = "\n".join([chunk.content[:200] + "..." for chunk in relevant_chunks[:2]])
        rag_prompt = f"Context: {context}\n\nQuestion: {query}\n\nAnswer based on the context:"
        
        response = await llm_adapter.complete(rag_prompt)
        
        if not response:
            logger.error("❌ Failed to generate RAG response")
            return False
        
        if verbose:
            logger.info(f"🎯 RAG Response: {response[:200]}...")
        
        logger.info("✅ RAG pipeline end-to-end test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ RAG pipeline test failed: {e}")
        return False


async def run_rag_tests(mode: str, query: Optional[str] = None, collection: Optional[str] = None, verbose: bool = False) -> bool:
    """Run RAG tests based on mode."""
    logger = setup_script_environment()
    
    logger.info("🚀 Starting RAG Pipeline Tests")
    logger.info("=" * 60)
    
    test_results = []
    
    if mode in ['quick', 'full', 'embeddings']:
        result = await test_embeddings(logger, verbose)
        test_results.append(('Embeddings', result))
    
    if mode in ['quick', 'full', 'search']:
        result = await test_database_search(logger, verbose, collection)
        test_results.append(('Database Search', result))
    
    if mode in ['quick', 'full', 'llm']:
        result = await test_llm_integration(logger, verbose)
        test_results.append(('LLM Integration', result))
    
    if mode == 'full' or query:
        test_query = query or "What is machine learning?"
        result = await test_rag_pipeline_end_to_end(logger, test_query, verbose, collection)
        test_results.append(('RAG Pipeline E2E', result))
    
    # Summary
    logger.info("=" * 60)
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    if passed == total:
        logger.info(f"🎉 All {total} RAG tests passed!")
        logger.info("✅ RAG pipeline is working correctly")
        return True
    else:
        logger.error(f"❌ {total - passed}/{total} tests failed")
        return False


def main():
    """Main function for RAG pipeline testing."""
    parser = argparse.ArgumentParser(description="Test RAG pipeline functionality")
    parser.add_argument('--mode', choices=['quick', 'full', 'embeddings', 'search', 'llm'], 
                       default='quick', help='Test mode')
    parser.add_argument('--query', help='Test query for RAG pipeline')
    parser.add_argument('--collection', help='Collection to test against')
    parser.add_argument('--verbose', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    async def run_tests():
        with ScriptContext() as ctx:
            success = await run_rag_tests(args.mode, args.query, args.collection, args.verbose)
            return 0 if success else 1
    
    return asyncio.run(run_tests())


if __name__ == '__main__':
    script_main(main)
