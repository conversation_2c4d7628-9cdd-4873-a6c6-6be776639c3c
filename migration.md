# BusinessLM Orchestration PoC — Migration Plan & Architecture

## 🧠 Multi*Agent Orchestration Overview

This PoC is designed around a **multi*agent orchestration*first architecture**, where multiple domain agents collaborate through a centralized planning and coordination mechanism. A dedicated **tooling system** enables structured and dynamic access to external tools such as RAG retrieval, web search, and future integrations.

### 🔄 Core Principles

* **Flat multi*agent architecture**: Agents like `CoCEO`, `Finance`, and `Marketing` operate as peers and can communicate.
* **Dedicated planner and coordinator**: `planner.py` breaks down tasks, `coordinator.py` delegates them.
* **Dynamic tool invocation**: Agents don't invoke tools directly; they delegate to a centralized tool system via `ToolRegistry`.
* **Structured task memory and state**: Task state flows through `state.py` and `memory.py`, enabling traceable, interruptible, and resumable orchestration.
* **Full orchestration traceability**: Every plan, agent interaction, and tool usage is logged and traceable in `tracer.py` and `events.py`.

***

## 🗂️ File Migration Strategy

### 🟢 Files to Migrate and Polish

#### **Database & RAG Components** ✅ *COMPLETED*

* ✅ `backend/rag/` — **RAG system simplified and completed**

  * Removed code duplication (eliminated \~560 lines of duplicate code)
  * Completed missing implementations in vector\_store.py and knowledge\_base.py
  * Fixed all broken imports and circular dependencies
  * Streamlined embeddings with factory pattern and fallback support
  * Consolidated retriever components (removed duplicate ContextWindowManager/QueryRewriter)
* `backend/app/db/postgres.py` — PostgreSQL connection *(clean up)*
* `backend/app/llm/adapters/` — LLM adapters *(clean interfaces)*

#### **Configuration & Utilities**

* `backend/app/core/config.py` — Configuration management
* `backend/app/core/errors.py` — Error handling
* Mock documents from `backend/data/mock_documents/`

### 🔴 Files to Rebuild from Scratch

#### **Orchestration System**

* All LangGraph components *(current implementation is too convoluted)*
* Agent implementations *(too much inheritance and complexity)*
* State management *(rebuild with orchestration*first design)*
* Tool system *(current is department*bound, need dynamic selection)*

#### **CLI & Testing** ✅ *COMPLETED*

* ✅ CLI tools *(rebuilt with clean subcommand structure)*
* ✅ Testing infrastructure *(comprehensive RAG and system testing)*

***

## 📁 New Repository Structure

```text
businesslm*orchestration*poc/
├── README.md
├── .env.example
├── .gitignore
│
├── app/
│   ├── __init__.py
│   ├── config.py
│   ├── errors.py
│   │
│   ├── core/
│   │   ├── __init__.py
│   │   ├── logging.py
│   │   └── database.py
│   │
│   ├── llm/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── openai.py
│   │   ├── anthropic.py
│   │   └── gemini.py
│   │
│   ├── rag/                    ✅ COMPLETED & SIMPLIFIED
│   │   ├── __init__.py         # Clean exports, no broken imports
│   │   ├── embeddings/         # Modular embedding implementations
│   │   │   ├── __init__.py
│   │   │   ├── base.py         # Abstract base class
│   │   │   ├── openai.py       # OpenAI embeddings
│   │   │   ├── huggingface.py  # HuggingFace embeddings
│   │   │   └── factory.py      # Factory with fallback support
│   │   ├── retriever/          # Retrieval components
│   │   │   ├── __init__.py
│   │   │   ├── retriever.py    # Base & Hybrid retrievers (simplified)
│   │   │   ├── context_window.py  # Context window management
│   │   │   └── query_rewriter.py  # Query enhancement
│   │   ├── knowledge_base.py   # Complete search implementation
│   │   ├── vector_store.py     # PostgreSQL/pgvector implementation
│   │   └── utils.py           # Configuration & initialization helpers
│   │
│   ├── orchestration/
│   │   ├── __init__.py
│   │   ├── state.py
│   │   ├── planner.py
│   │   ├── memory.py
│   │   ├── coordinator.py
│   │   └── graph.py
│   │
│   ├── agents/
│   │   ├── __init__.py
│   │   ├── base.py                  # Base agent interface
│   │   ├── co_ceo.py                # Planner & coordinator (has tools too)
│   │   ├── finance.py               # Finance agent — cost analysis, forecasting
│   │   └── marketing.py            # Marketing agent — campaign & competition insight
│   │
│   ├── tools/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── registry.py
│   │   └── web_search.py
│   │
│   └── tracing/
│       ├── __init__.py
│       ├── tracer.py
│       ├── visualizer.py
│       └── events.py
│
├── cli/                        ✅ COMPLETED
│   ├── __init__.py
│   ├── main.py                 # Unified CLI with subcommands
│   ├── commands/               # Command modules
│   │   ├── config.py          # Configuration validation
│   │   ├── database.py        # Database operations
│   │   ├── documents.py       # Document management
│   │   └── test.py           # Testing operations
│   └── utils.py               # CLI utilities
│
├── data/
│   ├── documents/
│   └── traces/
│
├── tests/
│   ├── __init__.py
│   ├── test_rag.py
│   ├── test_agents.py
│   ├── test_orchestration.py
│   └── test_tools.py
│
├── docs/                       ✅ PARTIALLY COMPLETED
│   ├── RAG_TESTING_GUIDE.md   # Comprehensive RAG testing documentation
│   ├── architecture.md        # TODO: System architecture
│   ├── orchestration.md       # TODO: Orchestration design
│   └── api.md                 # TODO: API documentation
│
└── scripts/                   ✅ COMPLETED & CONSOLIDATED
    ├── database_migrations.py  # Database migration management
    ├── load_knowledge_base_documents.py  # Document loading
    ├── setup_pgvector_db.py   # Database setup
    ├── system_integration_tests.py  # System testing
    ├── test_rag_pipeline.py   # RAG pipeline testing
    ├── test_utils.py          # Testing utilities
    ├── script_utils.py        # Consolidated script utilities
    └── validate_config.py     # Configuration validation
```

***

## 🗓️ 12*Day Implementation Timeline

### Days 1–2: Foundation Setup ✅ *COMPLETED*

**Day 1:**

* ✅ Create repo structure
* ✅ Migrate config, database, and LLM adapters
* ✅ Add basic logging & error handling
* ✅ Set up requirements and README
* ✅ Fix Alembic configuration and migration system
* ✅ Implement comprehensive CLI interface with subcommands

**Day 2:**

* ✅ Migrate RAG components
* ✅ Set up PostgreSQL + pgvector
* ✅ Create document loading scripts
* ✅ Test basic RAG pipeline
* ✅ Add RAG testing infrastructure with multiple modes
* ✅ Create comprehensive documentation (RAG\_TESTING\_GUIDE.md)
* ✅ Consolidate script utilities and remove duplications

### Days 3–4: Orchestration Core

**🧠 Design `state.py`**

* Define the **central orchestration state model** (`LangGraphState`), which all nodes will read from and update.
* Implement Enums:
  * `OrchestrationStep`: stages of the graph (e.g., `PLANNING`, `TASK_EXECUTION`, `REVIEW`, `DONE`).
  * `TaskType`: semantic purpose of each task (e.g., `REASONING`, `TOOLS`, `ACTION`).
  * `TaskStatus`: lifecycle of each task (`PENDING`, `IN_PROGRESS`, `COMPLETED`, `FAILED`).
* Add utility methods:
  * `get_task_by_id`: for safe and efficient task lookup during orchestration.
  * `is_terminal`: to check if the orchestration has reached its end (i.e., `current_step == DONE`).
* Use `@model_validator` to enforce integrity (e.g., `current_task_id` must reference a real task).

***

**🧠 Implement `planner.py` (Task Decomposition)**

* Accepts `user_input` from `LangGraphState` and outputs a structured list of `Task` instances.
* Responsible for:
  * Breaking down the query into discrete subtasks.
  * Setting task dependencies and types.
* Output:
  * Populates `state.tasks` with Task objects.
  * Sets `state.current_step` to `TASK_ASSIGNMENT`.

***

**🧠 Create `memory.py`**

* A shared memory interface to enrich context across steps.
* Feeds data into `state.context`, including:
  * Historical task traces.
  * RAG*relevant information.
  * Agent/system memory.
* Used by planner and agents to reason and personalize behavior.

***

**🧠 Define `coordinator.py`**

* The logic brain for progressing orchestration steps:
  * Assigns agents to available tasks.
  * Updates task statuses (`IN_PROGRESS`, `COMPLETED`, etc.).
  * Determines next `OrchestrationStep`.
* Makes use of:
  * `get_task_by_id`, `task.status`, and `task.dependencies`.
  * Routing decisions based on orchestration state.

***

**🧠 Build `graph.py` (LangGraph Definition)**

* Defines the node map of orchestration:
  * Each `OrchestrationStep` becomes a LangGraph node.
  * Transitions governed by `state.current_step`.
* Adds support for dynamic state evolution and branching logic.
* Attaches tracing to observe node inputs/outputs.

***

**🧠 Implement Tracing (`tracer.py`, `events.py`)**

* Track internal changes in the state at each node.
* Record agent outputs, tool usage, and transitions.
* Essential for debugging and later evaluation.

***

**🧪 Test Core Orchestration**

* Simulate one full round of planning → assignment → execution using mock agents.
* Confirm that:
  * State transitions follow logic.
  * Agents get valid inputs.
  * Tasks are updated correctly.

### Days 5–6: Agent Implementation

**🧠 Build `BaseAgent`**

* Define a common interface or abstract base class for all agents.
* Handles:
  * Core `run()` method for reasoning over a task.
  * Optional integration with tools or memory modules.
  * Input/output normalization for LangGraph compatibility.

***

**🧠 Implement `CoCEOAgent` with Planning**

* Acts as the primary orchestrator agent that interprets high*level user input.
* Responsibilities:
  * Generates or updates orchestration plans.
  * Interfaces with the planner module.
  * Initiates the `PLANNING` and `TASK_ASSIGNMENT` steps.
  * Can call tools directly (e.g., RAG, search) to inform decisions.

***

**🧠 Add Task Decomposition**

* Extend `CoCEOAgent` or planner to break down queries into:
  * Atomic tasks (`TaskType`)
  * Proper dependency trees (`task.dependencies`)
  * Logical assignments to other agents (`task.assigned_agent`)

***

**🧠 Provide Basic Reasoning Loop**

* Define how agents internally process:
  * `Task.description`, context, dependencies, and prior outputs.
* Should produce structured `Task.output` to be stored in state.

***

**🧠 Enable Direct RAG/WebSearch Tool Access from `CoCEO`**

* Allow the `CoCEO` agent to trigger tool use (e.g., `@websearch`, `@docquery`) when additional info is needed during planning or evaluation.
* Populate outputs in `state.tool_outputs`.

***

**🧠 Implement `FinanceAgent` and `MarketingAgent`**

* Specialized departmental agents with domain*specific knowledge.
* Use RAG, reasoning, or tools to complete assigned tasks.
* Must follow the `BaseAgent` interface and respect task context.

***

**🧠 Enable Agent Communication**

* Allow agent outputs to be routed and referenced by others.
* Mechanisms:
  * Shared context (`state.context`)
  * Direct dependency chaining (`task.dependencies`)
  * Output memory referencing (`agent_outputs[agent_id]`)

***

**🧪 Validate Multi-Agent Orchestration and Fallback Handling**

* Simulate real*world multi*step workflows:
  * `CoCEO → Finance + Marketing → Review`
* Handle:
  * Missing outputs
  * Failed agent responses (`TaskStatus.FAILED`)
  * Retry or fallback flows

***

### Days 7–8: Tool System & Web Search

**🛠️ Create Dynamic Tool System**

* Design an abstract interface for tools (`BaseTool`) and invocation logic.
* Capable of:
  * Accepting structured inputs
  * Producing standardized outputs
  * Handling errors gracefully

***

**🛠️ Build `ToolRegistry`**

* Central router for all available tools.
* Supports:
  * Tool lookup by name (e.g., `@websearch`)
  * Metadata access (e.g., input schema)
  * Dynamic loading (e.g., from config)

***

**🛠️ Implement `WebSearchTool`**

* First concrete tool implementation.
* Accepts a query and returns summarized web results.
* Powered by API (real or mock).

***

**🧠 Integrate Tool Results into Agent Output**

* Allow agents to embed tool outputs into their task results.
* Populate `tool_outputs` in state and use them for reasoning loops.

***

**🔗 Connect Tools with Orchestration**

* Expose tools as actions within the LangGraph nodes.
* Let orchestrator or agents decide when to trigger a tool (e.g., based on `TaskType.TOOLS`)

***

**🧾 Add Usage Logging**

* Record every tool invocation:
  * Input
  * Output
  * Tool name
  * Timestamp
* Used for debugging and future analytics.

***

**🚨 Implement Tool-Specific Error Handling**

* Catch and handle:
  * Timeouts
  * Malformed input
  * Missing results
* Allow tools to return structured error responses that don't break the orchestration flow.

### Days 9–10: CLI & Testing ✅ *COMPLETED*

* ✅ Build CLI interface
* ✅ Implement CLI testing hooks
* ✅ Add trace visualization in CLI
* ✅ Enable interactive test mode with multi*agent delegation inspection
* ✅ Implement comprehensive test suite
* ✅ Add full integration tests
* ✅ Build benchmarking tools
* ✅ Validate agent routing, tool usage, and trace logging end*to*end

### Days 11–12: Integration & Polish

* Run full*system tests
* Optimize performance
* Fix bugs
* Update documentation
* Final QA pass
* Create demo scenarios
* Polish CLI
* Prepare presentation

***

## 🔑 Key Implementation Principles

### 1. **Orchestration*First Design**

```python
class BaseAgent:
    def __init__(self, llm_adapter, tools=None):
        self.llm_adapter = llm_adapter
        self.tools = tools or []

    async def process_task(self, task, context):
        pass

    async def use_tool(self, task, context):
        for tool in self.tools:
            if tool.can_handle(task):
                return await tool.run(task, context)

class CoCEOAgent(BaseAgent):
    async def plan_execution(self, query):
        documents = await self.use_tool("search background", context={})
        # Use retrieved docs to create plan

    async def coordinate_agents(self, plan, agents):
        pass
```

### 2. **Dynamic Tool Selection**

```python
class ToolRegistry:
    def select_tools(self, task_type, context):
        pass

class WebSearchTool:
    def can_handle(self, task):
        pass
```

### 3. **Comprehensive Tracing**

```python
class OrchestrationTracer:
    def trace_planning(self, query, plan):
        pass

    def trace_agent_interaction(self, agent, task, result):
        pass

    def trace_tool_usage(self, tool, input, output):
        pass
```

📝 *Trace logs should include which agent handled which task, what tool was used (if any), and the results of each step — including direct tool usage by CoCEO.*

***

## 📦 Migration Strategy

### Week 1 (Days 1–7): Core Migration

* Extract reusable components (RAG, adapters, config)
* Refactor into simpler interfaces
* Build orchestration core from scratch

### Week 2 (Days 8–12): Integration & Testing

* Implement agents cleanly with planning capabilities
* Integrate tools dynamically and test multi*agent flow
* Build and test CLI end*to*end with trace replay

***

## ✅ Success Metrics (By Day 12)

**✅ COMPLETED (Days 1*2):**

* ✅ RAG with pgvector working
* ✅ CLI test and visualization tools in place
* ✅ Full integration test suite
* ✅ CLI supports inspecting multi*agent execution paths
* ✅ Database migration system operational
* ✅ Comprehensive testing infrastructure